import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CreationAttributes, Op } from 'sequelize';
import * as bcrypt from 'bcrypt';
import { User } from '../database/models/user.model';
import { Role } from '../database/models/role.model';
import { Company } from '../database/models/company.model';
import { Department } from '../database/models/department.model';
import { SurveyResponse } from '../database/models/survey-response.model';
import { UserDepartment } from 'src/database/models/user-department.model';
import { CreateUserDto, UpdateUserDto, UserResponseDto, UserListResponseDto } from './dto/user.dto';
import { EmailService } from '../common/services/email.service';
import { SurveyService } from '../survey/survey.service';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(Role)
    private readonly roleModel: typeof Role,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
    @InjectModel(Department)
    private readonly departmentModel: typeof Department,
    @InjectModel(SurveyResponse)
    private readonly surveyResponseModel: typeof SurveyResponse,
    @InjectModel(UserDepartment)
    private readonly userDepartmentModel: typeof UserDepartment,
    private readonly emailService: EmailService,
    private readonly surveyService: SurveyService,
  ) { }

  /**
   * Create a new user (CompanyAdmin only)
   */
  async createUser(
    companyId: string,
    dto: CreateUserDto,
    createdByUserId: string,
  ): Promise<UserResponseDto> {
    // Verify company exists
    const company = await this.companyModel.findOne({
      where: { id: companyId, isDeleted: false },
    });

    if (!company) {
      throw new NotFoundException('Company not found');
    }

    // Check if email already exists
    const existingUser = await this.userModel.findOne({
      where: {
        email: dto.email,
        isDeleted: false,
      },
    });


    if (existingUser) {
      throw new BadRequestException(`User with email '${dto.email}' already exists`);
    }

    // Get role by name
    const role = await this.roleModel.findOne({
      where: { name: dto.role, isDeleted: false },
    });

    const simplifiedRole = role?.get({ plain: true });

    if (!role) {
      throw new BadRequestException(`Role '${dto.role}' not found`);
    }

    // Validate departments based on role
    let departments: Department[] = [];
    let primaryDepartment: Department | null = null;

    if (dto.role === 'CompanyAdministrator') {
      // CompanyAdministrator can have multiple departments
      if (!dto.departmentIds || dto.departmentIds.length === 0) {
        throw new BadRequestException('CompanyAdministrator must be assigned to at least one department');
      }

      departments = await this.departmentModel.findAll({
        where: {
          id: { [Op.in]: dto.departmentIds },
          company_id: companyId,
          isDeleted: false
        },
      });

      if (departments.length !== dto.departmentIds.length) {
        throw new BadRequestException('One or more departments not found or do not belong to your company');
      }
    } else {
      // Other roles require single department
      if (!dto.departmentId) {
        throw new BadRequestException(`${dto.role} must be assigned to a department`);
      }

      primaryDepartment = await this.departmentModel.findOne({
        where: {
          id: dto.departmentId,
          company_id: companyId,
          isDeleted: false
        },
      });

      if (!primaryDepartment) {
        throw new BadRequestException('Department not found or does not belong to your company');
      }
      departments = [primaryDepartment];
    }

    // Validate supervisor if provided
    let supervisor: any = null;
    if (dto.supervisorId) {
      supervisor = await this.userModel.findOne({
        where: {
          id: dto.supervisorId,
          company_id: companyId,
          isDeleted: false
        },
        include: [{ model: Role }]
      });

      if (!supervisor) {
        throw new BadRequestException('Supervisor not found or does not belong to your company');
      }

      // Validate supervisor role
      if (!['Supervisor', 'CompanyManager', 'CompanyAdministrator'].includes(supervisor.role.name)) {
        throw new BadRequestException('Selected supervisor must have Supervisor, CompanyManager, or CompanyAdministrator role');
      }
    }

    // Generate temporary password
    const temporaryPassword = this.emailService.generateTemporaryPassword();
    const hashedPassword = await bcrypt.hash(temporaryPassword, 10);

    // Create user (using junction table for all department relationships)
    const user = await this.userModel.create({
      firstName: dto.firstName,
      lastName: dto.lastName,
      email: dto.email,
      password: hashedPassword,
      roleId: role.id,
      company_id: companyId,
      supervisor_id: dto.supervisorId || null,
      isTemporaryPassword: true,
      isActive: true,
      createdBy: createdByUserId,
    } as CreationAttributes<User>);

    // Create UserDepartment relationships
    const userDepartmentPromises = departments.map(dept =>
      this.userDepartmentModel.create({
        user_id: user.id,
        department_id: dept.id,
        isActive: true,
        createdBy: createdByUserId,
      } as CreationAttributes<UserDepartment>)
    );

    await Promise.all(userDepartmentPromises);

    // Send welcome email with temporary password
    try {
      await this.emailService.sendWelcomeEmail(
        user.email,
        user.firstName,
        user.lastName,
        temporaryPassword,
        company.name
      );
    } catch (error) {
      console.error('Failed to send welcome email:', error);
      // Don't fail user creation if email fails
    }

    return this.mapToResponseDto(user, simplifiedRole, primaryDepartment, supervisor, false, departments);
  }

  /**
   * Get users by company (CompanyAdmin can see all, Supervisor can see their team)
   */
  async getUsersByCompany(
    companyId: string,
    currentUserRole: string,
    currentUserId: string,
    filters?: {
      role?: string;
      department?: string;
      isActive?: boolean;
      page?: number;
      limit?: number;
    }
  ): Promise<UserListResponseDto> {
    const whereClause: any = {
      company_id: companyId,
      isDeleted: false,
    };

    // Role-based filtering
    if (currentUserRole === 'Supervisor') {
      // Supervisors can only see their direct reports
      whereClause.supervisor_id = currentUserId;
    }

    // Apply additional filters
    if (filters?.isActive !== undefined) {
      whereClause.isActive = filters.isActive;
    }

    // Build include array for joins
    const includeArray = [
      {
        model: Role,
        ...(filters?.role && { where: { name: filters.role } })
      },
      {
        model: UserDepartment,
        required: false,
        include: [
          {
            model: Department,
            required: false,
            ...(filters?.department && { where: { name: filters.department } })
          }
        ]
      },
      {
        model: User,
        as: 'supervisor',
        required: false,
        include: [{ model: Role }]
      }
    ];

    // Pagination
    const page = filters?.page || 1;
    const limit = filters?.limit || 10;
    const offset = (page - 1) * limit;

    const { rows: users, count: total } = await this.userModel.findAndCountAll({
      where: whereClause,
      include: includeArray,
      limit,
      offset,
      order: [['createdAt', 'DESC']],
    });

    const simplifiedUsers = users.map(user => user.get({ plain: true }));

    const userResponses = simplifiedUsers.map((user: any) => {
      // Extract departments from UserDepartment relationships
      const userDepartments = user.userDepartments?.map((ud: any) => ud.department).filter(Boolean) || [];
      const primaryDepartment = userDepartments.length > 0 ? userDepartments[0] : null;

      return this.mapToResponseDto(
        user,
        user.role,
        primaryDepartment,
        user.supervisor,
        false,
        userDepartments.length > 1 ? userDepartments : undefined
      );
    });

    return {
      users: userResponses,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get user by ID
   */
  async getUserById(
    userId: string,
    companyId: string,
    currentUserRole: string,
    currentUserId: string
  ): Promise<UserResponseDto> {
    const whereClause: any = {
      id: userId,
      company_id: companyId,
      isDeleted: false,
    };

    // Role-based access control
    if (currentUserRole === 'Supervisor') {
      // Supervisors can only see their direct reports or themselves
      whereClause[Op.or] = [
        { supervisor_id: currentUserId },
        { id: currentUserId }
      ];
    } else if (currentUserRole === 'Employee') {
      // Employees can only see themselves
      whereClause.id = userId;
    }

    const user: any = await this.userModel.findOne({
      where: whereClause,
      include: [
        {
          model: Role,
          required: true
        },
        {
          model: Department,
          required: true
        }
      ],
    });

    if (!user) {
      throw new NotFoundException('User not found or access denied');
    }

    let supervisor: any = null;
    if (user.supervisor_id) {
      supervisor = await this.userModel.findByPk(user.supervisor_id, {
        include: [{ model: Role }]
      });
    }

    const simplifiedUser = user.get({ plain: true });
    
    return this.mapToResponseDto(simplifiedUser, simplifiedUser.role, simplifiedUser.department, supervisor);
  }

  /**
   * Update user (CompanyAdmin only)
   */
  async updateUser(
    userId: string,
    companyId: string,
    dto: UpdateUserDto,
    updatedByUserId: string,
  ): Promise<UserResponseDto> {
    const user = await this.userModel.findOne({
      where: {
        id: userId,
        company_id: companyId,
        isDeleted: false
      },
      include: [{ model: Role }]
    });

    console.log("user==>", user)

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Prepare update data
    const updateData: any = {
      updatedBy: updatedByUserId,
    };

    // Update basic fields
    if (dto.firstName) updateData.firstName = dto.firstName;
    if (dto.lastName) updateData.lastName = dto.lastName;
    if (dto.email) {
      // Check if email is already taken by another user
      const existingUser = await this.userModel.findOne({
        where: {
          email: dto.email,
          id: { [Op.ne]: userId }, // Exclude current user
          isDeleted: false
        }
      });

      if (existingUser) {
        throw new BadRequestException(`Email '${dto.email}' is already taken by another user`);
      }

      updateData.email = dto.email;
    }
    if (dto.isActive !== undefined) updateData.isActive = dto.isActive;

    // Update role if provided
    if (dto.role) {
      const role = await this.roleModel.findOne({
        where: { name: dto.role, isDeleted: false },
      });

      if (!role) {
        throw new BadRequestException(`Role '${dto.role}' not found`);
      }

      updateData.roleId = role.id;
    }

    console.log("updateData==>", updateData)
    console.log("dto==>", dto)

    let departments: Department[] = [];
    let primaryDepartment: Department | null = null;

    // Only validate and process departments if department information is being updated
    if (dto.role || dto.departmentId || dto.departmentIds) {
      const roleToCheck = dto.role || user.role.name;

      if (roleToCheck === 'CompanyAdministrator') {
        // CompanyAdministrator can have multiple departments
        if (dto.departmentIds) {
          if (dto.departmentIds.length === 0) {
            throw new BadRequestException('CompanyAdministrator must be assigned to at least one department');
          }

          departments = await this.departmentModel.findAll({
            where: {
              id: { [Op.in]: dto.departmentIds },
              company_id: companyId,
              isDeleted: false
            },
          });

          if (departments.length !== dto.departmentIds.length) {
            throw new BadRequestException('One or more departments not found or do not belong to your company');
          }
        }
      } else {
        // Other roles require single department
        if (dto.departmentId) {
          primaryDepartment = await this.departmentModel.findOne({
            where: {
              id: dto.departmentId,
              company_id: companyId,
              isDeleted: false
            },
          });

          if (!primaryDepartment) {
            throw new BadRequestException('Department not found or does not belong to your company');
          }
          departments = [primaryDepartment];
        }
      }
    }

    // Update UserDepartment relationships if department info is provided
    if (departments.length > 0) {
      // Get current UserDepartment relationships
      const currentUserDepartments = await this.userDepartmentModel.findAll({
        where: {
          user_id: userId,
          isDeleted: false
        }
      });

      const currentDepartmentIds = currentUserDepartments.map(ud => ud.department_id);
      const newDepartmentIds = departments.map(dept => dept.id);

      // Find departments to remove (in current but not in new)
      const departmentsToRemove = currentDepartmentIds.filter(id => !newDepartmentIds.includes(id));

      // Find departments to add (in new but not in current)
      const departmentsToAdd = newDepartmentIds.filter(id => !currentDepartmentIds.includes(id));

      // Soft delete removed departments
      if (departmentsToRemove.length > 0) {
        await this.userDepartmentModel.update(
          {
            isDeleted: true,
            updatedBy: updatedByUserId
          },
          {
            where: {
              user_id: userId,
              department_id: { [Op.in]: departmentsToRemove },
              isDeleted: false
            }
          }
        );
      }

      // Add new departments (only create if they don't exist)
      if (departmentsToAdd.length > 0) {
        const userDepartmentPromises = departmentsToAdd.map(async (departmentId) => {
          // Check if a soft-deleted record exists that we can reactivate
          const existingRecord = await this.userDepartmentModel.findOne({
            where: {
              user_id: userId,
              department_id: departmentId,
              isDeleted: true
            }
          });

          if (existingRecord) {
            // Reactivate existing soft-deleted record
            return existingRecord.update({
              isDeleted: false,
              isActive: true,
              updatedBy: updatedByUserId
            });
          } else {
            // Create new record
            return this.userDepartmentModel.create({
              user_id: userId,
              department_id: departmentId,
              isActive: true,
              createdBy: updatedByUserId,
            } as CreationAttributes<UserDepartment>);
          }
        });

        await Promise.all(userDepartmentPromises);
      }
    }



    // Update supervisor if provided
    if (dto.supervisorId) {
      const supervisor = await this.userModel.findOne({
        where: {
          id: dto.supervisorId,
          company_id: companyId,
          isDeleted: false
        },
        include: [{ model: Role }]
      });

      if (!supervisor) {
        throw new BadRequestException('Supervisor not found or does not belong to your company');
      }

      // Validate supervisor role
      if (!['Supervisor', 'CompanyManager', 'CompanyAdmin'].includes(supervisor.role.name)) {
        throw new BadRequestException('Selected supervisor must have Supervisor, CompanyManager, or CompanyAdmin role');
      }

      updateData.supervisor_id = dto.supervisorId;
    }

    // Update user
    console.log('About to update user with data:', updateData);
    try {
      await user.update(updateData);
      console.log('User updated successfully');
    } catch (updateError) {
      console.error('Error updating user:', updateError);
      throw updateError;
    }

    // Fetch updated user with relations
    const updatedUser: any = await this.userModel.findByPk(userId, {
      include: [
        { model: Role },
        {
          model: UserDepartment,
          where: { isDeleted: false },
          required: false,
          include: [{ model: Department }]
        }
      ],
    });

    if (!updatedUser) {
      throw new NotFoundException('Updated user not found');
    }

    let supervisor: any = null;
    if (updatedUser.supervisor_id) {
      supervisor = await this.userModel.findByPk(updatedUser.supervisor_id, {
        include: [{ model: Role }]
      });
    }

    const simplifiedUpdatedUser = updatedUser.get({ plain: true });

    // Get all departments for this user
    const userDepartments = updatedUser.userDepartments || [];
    const allDepartments = userDepartments.map((ud: any) => ud.department);

    // For backward compatibility, set primary department as the first one
    const updatedPrimaryDepartment = allDepartments.length > 0 ? allDepartments[0] : null;

    return this.mapToResponseDto(updatedUser, simplifiedUpdatedUser.role, updatedPrimaryDepartment, supervisor, false, allDepartments);
  }

  /**
   * Delete user (CompanyAdmin only) - Soft delete
   * Also soft deletes all survey responses for the user
   */
  async deleteUser(
    userId: string,
    companyId: string,
    deletedByUserId: string,
  ): Promise<{ message: string }> {
    const user = await this.userModel.findOne({
      where: {
        id: userId,
        company_id: companyId,
        isDeleted: false
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Soft delete user
    await user.update({
      isDeleted: true,
      deletedBy: deletedByUserId,
      isActive: false,
    });

    // Soft delete all survey responses for this user
    await this.surveyResponseModel.update(
      {
        isDeleted: true,
        deletedBy: deletedByUserId,
        isActive: false,
      },
      {
        where: {
          user_id: userId,
          isDeleted: false
        }
      }
    );

    return { message: 'User and associated survey responses deleted successfully' };
  }

  /**
   * Get all employees for a specific company
   */
  async getEmployeesByCompany(
    companyId: string,
    filters?: {
      isActive?: boolean;
      // department?: string;
      nameStartsWith?: string;
      page?: number;
      limit?: number;
    }
  ): Promise<UserListResponseDto> {

    // Verify company exists
    const company = await this.companyModel.findOne({
      where: { id: companyId, isDeleted: false },
    });

    if (!company) {
      throw new NotFoundException('Company not found');
    }


    // Get all roles except CompanyAdministrator and CompanyAdmin
    const excludedRoles = await this.roleModel.findAll({
      where: {
        name: { [Op.in]: ['CompanyAdministrator', 'CompanyAdmin'] },
        isDeleted: false
      },
      attributes: ['id']
    });

    const excludedRoleIds = excludedRoles.map(role => role.id);

    const whereClause: any = {
      company_id: companyId,
      roleId: { [Op.notIn]: excludedRoleIds }, // Get all roles except CompanyAdministrator and CompanyAdmin
      isDeleted: false,
    };

    // ====> It is for the to fetch only the employees

    // const employeeRole: any = await this.roleModel.findOne({
    //   where: { name: 'Employee', isDeleted: false },
    // });


    // const simplifiedEmployeeRole = employeeRole.get({ plain: true });

    // if (!employeeRole.id) {
    //   throw new NotFoundException('Employee role not found');
    // }

    // const whereClause: any = {
    //   company_id: companyId,
    //   roleId: simplifiedEmployeeRole.id, // Only get users with Employee role
    //   isDeleted: false,
    // };

    // No role-based restrictions - any authenticated user can see all employees

    // Apply additional filters
    if (filters?.isActive !== undefined) {
      whereClause.isActive = filters.isActive;
    }

    // Filter by name starting with specific letter
    if (filters?.nameStartsWith) {
      const letter = filters.nameStartsWith.toUpperCase();
      // Validate that it's a single letter
      if (letter.length === 1 && /^[A-Z]$/.test(letter)) {
        whereClause.firstName = {
          [Op.like]: `${letter}%`
        };
      }
    }

    // Pagination
    // const page = filters?.page || 1;
    // const limit = filters?.limit || 10;
    // const offset = (page - 1) * limit;

    // Fetch users with their roles and departments
    const userResponse = await this.userModel.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Role,
          required: true
        },
        {
          model: Department,
          required: true
        }
      ],
      order: [['firstName', 'ASC'], ['lastName', 'ASC']],
    });

    // Convert raw Sequelize users to plain objects
    const users = userResponse.rows.map(user => user.get({ plain: true }));


    // Get supervisor info and survey submission status for each user
    const usersWithDetails = await Promise.all(
      users.map(async (user: any) => {
        let supervisor = null;

        if (user.supervisor_id) {
          try {
            const supervisorRecord = await this.userModel.findByPk(user.supervisor_id, {
              include: [{ model: Role }],
            });
            // supervisor = supervisorRecord?.get({ plain: true }) || null;
          } catch (error) {
            console.warn(`Failed to fetch supervisor ${user.supervisor_id} for user ${user.id}:`, error.message);
          }
        }

        // Check if user has submitted survey today
        let hasSubmittedSurveyToday = false;
        try {
          hasSubmittedSurveyToday = await this.surveyService.hasUserSubmittedSurveyToday(user.id, companyId);
        } catch (error) {
          console.warn(`Failed to check survey submission for user ${user.id}:`, error.message);
          // Default to false if check fails
          hasSubmittedSurveyToday = false;
        }

        return this.mapToResponseDto(user, user.Role, user.department, supervisor, hasSubmittedSurveyToday);
      })
    );

    return {
      users: usersWithDetails,
      // pagination: {
      //   total: count,
      //   // page,
      //   // limit,
      //   // totalPages: Math.ceil(count / limit),
      // },
    };
  }

  /**
   * Map User model to response DTO with role and department names
   */
  private mapToResponseDto(
    user: any,
    role: any,
    department?: any,
    supervisor?: any,
    hasSubmittedSurveyToday?: boolean,
    departments?: any[]
  ): UserResponseDto {
    // Handle case where role might be undefined or attached to user object
    const userRole = role || user.role;

    if (!userRole) {
      console.error(`User ${user.id} role debug:`, {
        user: {
          id: user.id,
          email: user.email,
          roleId: user.roleId,
          role: user.role
        },
        passedRole: role
      });
      throw new BadRequestException(`User ${user.email || user.id} does not have a role assigned`);
    }

    // Ensure userRole has required properties
    if (!userRole.id || !userRole.name) {
      console.error(`Invalid role data for user ${user.id}:`, userRole);
      throw new BadRequestException(`Invalid role data for user ${user.email || user.id}`);
    }

    return {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      companyId: user.company_id,
      role: {
        id: userRole.id,
        name: userRole.name,
      },
      department: (department && department.id) ? {
        id: department.id,
        name: department.name,
      } : undefined,
      departments: departments && departments.length > 0 ? departments.map(dept => ({
        id: dept.id,
        name: dept.name,
      })) : undefined,
      supervisor: supervisor ? {
        id: supervisor.id,
        firstName: supervisor.firstName,
        lastName: supervisor.lastName,
        role: supervisor.role?.name || 'Unknown',
      } : undefined,
      isActive: user.isActive,
      isTemporaryPassword: user.isTemporaryPassword,
      hasSubmittedSurveyToday: hasSubmittedSurveyToday || false,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      createdBy: user.createdBy,
      updatedBy: user.updatedBy,
    };
  }
}
